/**
 * Utility functions for fetching destination data from the backend API
 * with caching to reduce API calls
 */

import { SimpleCache, getHeaders } from "./common";

// Initialize the destination cache
const destinationCache = new SimpleCache(5 * 60 * 1000); // 5 minutes TTL

// Types for destination data
export interface DestinationImage {
  url: string;
  alt?: string;
}

export interface DestinationFAQ {
  id: string;
  question: string;
  answer: string;
  destination_id: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
}

export interface Destination {
  id: string;
  name: string;
  description: string;
  country: string;
  tagline?: string;
  images: DestinationImage[] | string[];
  handle?: string;
  slug?: string;
  is_featured?: boolean;
  is_active?: boolean;
  property_count?: number;
  activities?: string[];
  faqs?: DestinationFAQ[];
}

/**
 * Fetch all destinations with pagination
 * @param options - Options for fetching destinations
 * @returns The destinations data and total count
 */
export async function fetchDestinations(options: {
  limit?: number;
  offset?: number;
  isFeatured?: boolean;
  searchQuery?: string;
  skipCache?: boolean;
}): Promise<{ destinations: Destination[]; count: number }> {
  try {
    const {
      limit = 10,
      offset = 0,
      isFeatured,
      searchQuery,
      skipCache = false,
    } = options;

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append("limit", limit.toString());
    queryParams.append("offset", offset.toString());
    queryParams.append("is_active", "true");


    
    if (isFeatured) {
      queryParams.append("is_featured", "true");
    }
    console.log('queryParams',queryParams,isFeatured)

    // Only append search if it's not empty
    if (searchQuery && searchQuery !== "") {
      queryParams.append("search", searchQuery);
    }

    // Create a cache key based on the query parameters
    const cacheKey = `destinations_${queryParams.toString()}`;

    // Check cache first if not skipping cache
    if (!skipCache) {
      const cachedData = destinationCache.get<{
        destinations: Destination[];
        count: number;
      }>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/destinations?${queryParams.toString()}`;

    console.log("Destinations API URL:", url);

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    const result = {
      destinations: data?.destinations || [],
      count: data?.count || 0,
    };

    // Store in cache
    destinationCache.set(cacheKey, result);

    return result;
  } catch (e) {
    console.error("Error fetching destinations:", e);
    return { destinations: [], count: 0 };
  }
}

/**
 * Fetch featured destinations
 * @param limit - Maximum number of destinations to fetch
 * @returns The featured destinations
 */
export async function fetchFeaturedDestinations(
  limit: number = 4
): Promise<Destination[]> {
  const { destinations } = await fetchDestinations({
    limit,
    isFeatured: true,
  });

  console.log('destinations,',destinations)

  return destinations;
}

/**
 * Fetch a single destination by its slug
 * @param slug - The destination slug/handle
 * @returns The destination data or null if not found
 */
export async function fetchDestinationBySlug(
  slug: string,
  skipCache: boolean = false
): Promise<Destination | null> {
  try {
    // Create a cache key for this destination
    const cacheKey = `destination_${slug}`;

    // Check cache first if not skipping cache
    if (!skipCache) {
      const cachedData = destinationCache.get<Destination>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/destinations/${slug}`;

    console.log('url', url);

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data?.destination?.[0]) {
      return null;
    }

    const destination = data.destination[0];

    // Store in cache
    destinationCache.set(cacheKey, destination);

    return destination;
  } catch (e) {
    console.error("Error fetching destination by slug:", e);
    return null;
  }
}

/**
 * Fetch a single destination by its ID
 * @param id - The destination ID
 * @returns The destination data or null if not found
 */
export async function fetchDestinationById(
  id: string,
  skipCache: boolean = false
): Promise<Destination | null> {
  try {
    // Create a cache key for this destination
    const cacheKey = `destination_id_${id}`;

    // Check cache first if not skipping cache
    if (!skipCache) {
      const cachedData = destinationCache.get<Destination>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/destinations/${id}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data?.destination?.[0]) {
      return null;
    }

    const destination = data.destination[0];

    // Store in cache
    destinationCache.set(cacheKey, destination);

    return destination;
  } catch (e) {
    console.error("Error fetching destination by ID:", e);
    return null;
  }
}



/**
 * Fetch destinations for search suggestions
 * @param limit - Maximum number of destinations to fetch
 * @returns The destinations for search suggestions
 */
export async function fetchDestinationsForSearch(
  limit: number = 5,
  skipCache: boolean = false
): Promise<Destination[]> {
  try {
    // Create a cache key for search destinations
    const cacheKey = `search_destinations_${limit}`;

    // Check cache first if not skipping cache
    if (!skipCache) {
      const cachedData = destinationCache.get<Destination[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    const { destinations } = await fetchDestinations({
      limit,
      isFeatured: true,
      skipCache,
    });

    // Store in cache
    destinationCache.set(cacheKey, destinations);

    return destinations;
  } catch (e) {
    console.error("Error fetching destinations for search:", e);
    return [];
  }
}

/**
 * Fetch destinations for filters
 * @param limit - Maximum number of destinations to fetch
 * @returns The destinations for filters
 */
export async function fetchDestinationsForFilters(
  limit: number = 10,
  skipCache: boolean = false
): Promise<Destination[]> {
  try {
    // Create a cache key for filter destinations
    const cacheKey = `filter_destinations_${limit}`;

    // Check cache first if not skipping cache
    if (!skipCache) {
      const cachedData = destinationCache.get<Destination[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    const { destinations } = await fetchDestinations({
      limit,
      skipCache,
    });

    // Store in cache
    destinationCache.set(cacheKey, destinations);

    return destinations;
  } catch (e) {
    console.error("Error fetching destinations for filters:", e);
    return [];
  }
}

/**
 * Interface for hotel data
 */
export interface Hotel {
  id: string;
  name: string;
  description?: string;
  location?: string;
  stars?: number;
  rating?: number;
  price?: number;
  images?: string[];
  amenities?: string[];
  rooms?: any[];
}

/**
 * Fetch hotels by destination
 * @param destinationSlug - The destination slug/handle
 * @param limit - Maximum number of hotels to fetch
 * @param skipCache - Whether to skip the cache
 * @returns The hotels for the destination
 */
export async function fetchHotelsByDestination(
  destinationSlug: string,
  limit: number = 10,
  skipCache: boolean = false
): Promise<{ hotels: Hotel[]; count: number }> {
  try {
    // Create a cache key for hotels by destination
    const cacheKey = `hotels_${destinationSlug}_${limit}`;

    // Check cache first if not skipping cache
    if (!skipCache) {
      const cachedData = destinationCache.get<{
        hotels: Hotel[];
        count: number;
      }>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append("limit", limit.toString());
    queryParams.append("destination", destinationSlug);

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/hotels?${queryParams.toString()}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    const result = {
      hotels: data?.hotels || [],
      count: data?.count || 0,
    };

    // Store in cache
    destinationCache.set(cacheKey, result);

    return result;
  } catch (e) {
    console.error("Error fetching hotels by destination:", e);
    return { hotels: [], count: 0 };
  }
}
